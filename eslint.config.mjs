import { FlatCompat } from '@eslint/eslintrc';
import js from '@eslint/js';
import prettierConfig from 'eslint-config-prettier';
import security from 'eslint-plugin-security';
import securityNode from 'eslint-plugin-security-node';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all,
});

export default [
  // Base JavaScript configuration
  js.configs.recommended,

  // Next.js configuration using compat
  ...compat.extends('next/core-web-vitals', 'next/typescript'),

  // Security configuration
  {
    name: 'security-config',
    files: ['**/*.{js,jsx,ts,tsx}'],
    plugins: {
      security,
    },
    rules: {
      ...security.configs.recommended.rules,
      'security/detect-object-injection': 'error',
      'security/detect-non-literal-regexp': 'error',
      'security/detect-unsafe-regex': 'error',
      'security/detect-buffer-noassert': 'error',
      'security/detect-child-process': 'error',
      'security/detect-disable-mustache-escape': 'error',
      'security/detect-eval-with-expression': 'error',
      'security/detect-no-csrf-before-method-override': 'error',
      'security/detect-non-literal-fs-filename': 'error',
      'security/detect-non-literal-require': 'error',
      'security/detect-possible-timing-attacks': 'error',
      'security/detect-pseudoRandomBytes': 'error',
    },
  },

  // Node.js Security configuration
  {
    name: 'security-node-config',
    files: ['**/*.{js,jsx,ts,tsx}'],
    plugins: {
      'security-node': securityNode,
    },
    rules: {
      // SQL注入防护
      'security-node/detect-sql-injection': 'error',
      // NoSQL注入防护
      'security-node/detect-nosql-injection': 'error',
      // HTML注入检测
      'security-node/detect-html-injection': 'error',
      // 不安全正则表达式检测
      'security-node/non-literal-reg-expr': 'error',
      // 不安全的随机数生成
      'security-node/detect-insecure-randomness': 'warn',
      // 危险的重定向
      'security-node/detect-dangerous-redirects': 'error',
      // eval表达式检测
      'security-node/detect-eval-with-expr': 'error',
      // 不当异常处理
      'security-node/detect-improper-exception-handling': 'warn',
      // 非字面量require调用
      'security-node/detect-non-literal-require-calls': 'error',
      // 可能的时序攻击
      'security-node/detect-possible-timing-attacks': 'warn',
      // 未处理的异步错误
      'security-node/detect-unhandled-async-errors': 'error',
      // 未处理的事件错误
      'security-node/detect-unhandled-event-errors': 'error',
      // Cookie安全配置错误
      'security-node/detect-security-missconfiguration-cookie': 'error',
      // SSL禁用检测
      'security-node/disable-ssl-across-node-server': 'error',
    },
  },

  // Code complexity and quality rules (最严格企业级标准)
  {
    name: 'code-quality-config',
    files: ['**/*.{js,jsx,ts,tsx}'],
    rules: {
      // Complexity rules (最严格标准)
      'complexity': ['error', 10], // 降低到10，更严格
      'max-depth': ['error', 4], // 降低到4层，更严格
      'max-lines-per-function': ['error', 80], // 降低到80行，更严格
      'max-params': ['error', 5], // 降低到5个参数，更严格
      'max-nested-callbacks': ['error', 3], // 降低到3层，更严格
      'max-lines': ['error', 500], // 文件最大行数
      'max-statements': ['error', 30], // 函数最大语句数
      'max-statements-per-line': ['error', { max: 1 }], // 每行最大语句数

      // Code quality rules (最严格)
      'no-console': 'error', // 升级为error，生产环境不允许console
      'no-debugger': 'error',
      'no-alert': 'error',
      'no-var': 'error',
      'prefer-const': 'error',
      'no-duplicate-imports': 'error',
      'no-unused-expressions': 'error',
      'no-unused-vars': 'error',
      'no-undef': 'error',
      'no-unreachable': 'error',
      'no-unreachable-loop': 'error',

      // Best practices (最严格)
      'eqeqeq': ['error', 'always'],
      'no-eval': 'error',
      'no-implied-eval': 'error',
      'no-new-func': 'error',
      'no-script-url': 'error',
      'no-self-compare': 'error',
      'no-sequences': 'error',
      'no-throw-literal': 'error',
      'no-unmodified-loop-condition': 'error',
      'no-useless-call': 'error',
      'no-useless-concat': 'error',
      'no-useless-return': 'error',
      'prefer-promise-reject-errors': 'error',
      'radix': 'error',
      'yoda': 'error',

      // 安全相关 (最严格)
      'no-new-wrappers': 'error',
      'no-proto': 'error',
      'no-return-assign': 'error',
      'no-return-await': 'error',
      'no-void': 'error',
      'no-with': 'error',
      'require-await': 'error',

      // 代码风格 (最严格)
      'array-callback-return': 'error',
      'block-scoped-var': 'error',
      'consistent-return': 'error',
      'default-case': 'error',
      'default-case-last': 'error',
      'dot-notation': ['error', { allowPattern: '^[A-Z_]+$' }], // Allow env vars like process.env['NODE_ENV']
      'guard-for-in': 'error',
      'no-caller': 'error',
      'no-constructor-return': 'error',
      'no-else-return': 'error',
      'no-empty-function': 'error',
      'no-extend-native': 'error',
      'no-extra-bind': 'error',
      'no-floating-decimal': 'error',
      'no-implicit-coercion': 'error',
      'no-implicit-globals': 'error',
      'no-iterator': 'error',
      'no-labels': 'error',
      'no-lone-blocks': 'error',
      'no-loop-func': 'error',
      'no-magic-numbers': [
        'warn',
        { ignore: [0, 1, -1], ignoreArrayIndexes: true },
      ],
      'no-multi-assign': 'error',
      'no-new': 'error',
      'no-new-object': 'error',
      'no-octal-escape': 'error',
      'no-param-reassign': 'error',
      'no-plusplus': ['error', { allowForLoopAfterthoughts: true }],
      'no-restricted-syntax': [
        'error',
        'ForInStatement',
        'LabeledStatement',
        'WithStatement',
      ],
      'no-shadow': 'error',
      'no-ternary': 'off', // 允许三元运算符，但要谨慎使用
      'no-underscore-dangle': 'error',
      'no-unneeded-ternary': 'error',
      'no-unused-private-class-members': 'error',
      'prefer-arrow-callback': 'error',
      'prefer-destructuring': 'error',
      'prefer-exponentiation-operator': 'error',
      'prefer-object-spread': 'error',
      'prefer-rest-params': 'error',
      'prefer-spread': 'error',
      'prefer-template': 'error',
    },
  },

  // Prettier configuration (must be last to override conflicting rules)
  prettierConfig,

  // Global ignores
  {
    ignores: [
      'node_modules/**',
      '.next/**',
      'out/**',
      'build/**',
      'dist/**',
      '*.config.js',
      '*.config.mjs',
      'public/**',
      '.env*',
      'coverage/**',
      '*.d.ts',
      'scripts/**',
      'reports/**',
      'jest.setup.js',
      'jest.config.js',
    ],
  },
];
